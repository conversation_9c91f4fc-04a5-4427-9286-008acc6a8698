import numpy as np
import pandas as pd
from scipy.stats import genextreme
from scipy.special import gamma


def gev_fit(data):
    """
    拟合GEV分布参数
    :param data: 年最大降水序列
    :param method: 参数估计方法('MLE'或'PWM')
    :return: 形状参数c, 位置参数loc, 尺度参数scale
    """

    # 最大似然估计
    params = genextreme.fit(data)
    c = params[0]  # 形状参数(注意scipy中c = -ξ)
    loc = params[1]
    scale = params[2]
    return c, loc, scale

def gev_fit_pwm(data):
    """
    使用概率权重矩法(PWM)估计GEV参数
    """
    data_sorted = np.sort(data)
    n = len(data_sorted)
    
    # 计算概率权重矩
    b0 = np.mean(data_sorted)
    b1 = np.mean(data_sorted * (np.arange(1, n+1) - 1) / (n - 1))
    b2 = np.mean(data_sorted * (np.arange(1, n+1) - 1) * (np.arange(1, n+1) - 2) / ((n - 1) * (n - 2)))
    
    # 估计参数
    c = 7.8590 * (2*b1 - b0) / (3*b1 - b0) - 2.9554 * (2*b1 - b0)/(3*b1 - b0)**2
    scale = (2*b1 - b0) * c / (gamma(1 + c) * (1 - 2**(-c)))
    loc = b0 - scale * (1 - gamma(1 + c)) / c
    
    return -c, loc, scale  # 注意scipy中形状参数c = -ξ

def calculate_gev_return_level(c, loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param c: 形状参数(注意scipy中c = -ξ)
    :param loc: 位置参数
    :param scale: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return genextreme.ppf(non_exceedance_prob, c, loc=loc, scale=scale)

# 示例使用
if __name__ == "__main__":
    
    
    file_path = r'D:\python\Return_period_extremum\重现期验证数据\56247_1440min_copy.txt'
    T = pd.read_csv(file_path, sep = '\t').values

    precip = T[:,1]
    
    # 拟合GEV分布(最大似然估计)
    c, loc, scale = gev_fit(precip)
    print(f"Estimated parameters (MLE): shape c = {c:.4f}, loc = {loc:.2f}, scale = {scale:.2f}")
    print(f"Equivalent ξ = {-c:.4f}")  # ξ = -c
    
    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\Bureau_data\End_result\gev1.txt", "w", encoding= 'utf-8') as file:
        file.write(f"k = {c:.3f}, sigma = {scale:.3f}, mu = {loc:.3f} \n")
        for rp in return_periods:
            rl = calculate_gev_return_level(c, loc, scale, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
    
