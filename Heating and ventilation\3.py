#In[0]:加载数据
import pandas as pd
import numpy as np

hour_path = r'D:\Data verification\Heating and ventilation\data\SURF_CHN_MUL_HOR.xlsx'
day_path = r'D:\Data verification\Heating and ventilation\data\SURF_CHN_MUL_DAY.xlsx'
month_path = r'D:\Data verification\Heating and ventilation\data\SURF_CHN_MUL_MON.xlsx'

hour_data = pd.read_excel(hour_path)
hour_data['资料时间'] = pd.to_datetime(hour_data['资料时间'])
day_data = pd.read_excel(day_path)
day_data['资料时间'] = pd.to_datetime(day_data['资料时间'])
month_data = pd.read_excel(month_path)
month_data['资料时间'] = pd.to_datetime(month_data['资料时间'])

hour_data.replace([999999, 999998, 999990], np.nan, inplace=True)
day_data.replace([999999, 999998, 999990], np.nan, inplace=True)
month_data.replace([999999, 999998, 999990], np.nan, inplace=True)

columns_to_fill = {
    'hour_data': ['温度/气温', '相对湿度'],
    'day_data': ['平均气温', '平均相对湿度'],
    'month_data': ['平均气温', '平均相对湿度']
}

for df_name, columns in columns_to_fill.items():
    df = globals()[df_name]
    for col in columns:
        df[col] = df[col].ffill() 

#In[1]:提取数据
def Extract_data(start_date, end_date,value_name):
    start_year = pd.to_datetime(start_date).year  
    end_year = pd.to_datetime(end_date).year    
    
    hour_data_filtered = hour_data[(hour_data['资料时间'].dt.year >= start_year) & 
                                  (hour_data['资料时间'].dt.year <= end_year)]
    day_data_filtered = day_data[(day_data['资料时间'].dt.year >= start_year) & 
                                (day_data['资料时间'].dt.year <= end_year)]
    month_data_filtered = month_data[(month_data['资料时间'].dt.year >= start_year) & 
                                    (month_data['资料时间'].dt.year <= end_year)]
    
    result = {
        'hour_data': hour_data_filtered,
        'day_data': day_data_filtered,
        'month_data': month_data_filtered
    }
    return result[value_name]


def calculate_value(data, values):
    max_year = data['资料时间'].dt.year.max()
    min_year = data['资料时间'].dt.year.min()
    value = (max_year - min_year + 1)*values
    return value

def round_data(x, decimals):
    multiplier = 10 ** decimals
    return np.floor(x * multiplier + 0.5) / multiplier

# In[2]:计算函数

# 供暖计算室外温度
def Heating_calculates_the_outside_temperature(data,values):
    daily_temperature = data['平均气温'].sort_values()
    value = calculate_value(data,values)
    temperature = daily_temperature.values[value]
    return round_data(temperature,1)

# 冬季通风室外计算温度
def Calculate_the_temperature_outside_the_ventilation_room_in_winter(data_month,data_day):
    max_year = data_month['资料时间'].dt.year.max()
    min_year = data_month['资料时间'].dt.year.min()
    result =  []
    for year in range(min_year, max_year + 1):
        year_data = data_month[data_month['资料时间'].dt.year == year]
        min_T_data = year_data['平均气温'].min()
        min_T_date = year_data[year_data['平均气温'] == min_T_data]['资料时间'].values[0]
        min_T_date = pd.to_datetime(min_T_date).month
        min_T_data = data_day[(data_day['资料时间'].dt.year == year) & (data_day['资料时间'].dt.month == min_T_date)]['平均气温'].mean()
        result.append(min_T_data)
    result = np.array(result).mean()
    
    return round(result,1)


# 冬季空调室外计算温度
def In_winter_the_air_conditioner_calculates_the_temperature_outside(data):
    daily_temperature = data['平均气温'].sort_values()
    values = calculate_value(data,1)
    temperature = daily_temperature.values[values]
    return temperature

# 冬季空调室外计算相对湿度
def Relative_humidity_is_calculated_outside_the_air_conditioner_in_winter(data_month,data_day):
    max_year = data_month['资料时间'].dt.year.max()
    min_year = data_month['资料时间'].dt.year.min()
    result =  []
    for year in range(min_year, max_year + 1):
        year_data = data_month[data_month['资料时间'].dt.year == year]
        min_T_data = year_data['平均气温'].min()
        min_T_date = year_data[year_data['平均气温'] == min_T_data]['资料时间'].values[0]
        min_T_date = pd.to_datetime(min_T_date).month
        min_T_data = data_day[(data_day['资料时间'].dt.year == year) & (data_day['资料时间'].dt.month == min_T_date)]['平均相对湿度'].mean()
        result.append(min_T_data)
    result = np.array(result).mean()
    return round_data(result,1)

# 夏季空调室外计算干球温度
def In_summer_the_air_conditioner_calculates_the_dry_bulb_temperature_outside(data,values):
    daily_temperature = data['温度/气温'].sort_values(ascending=False)
    value = calculate_value(data,values)
    temperature = daily_temperature.values[value]
    return round_data(temperature,1)


# 夏季通风室外计算温度
def The_temperature_is_calculated_outside_the_ventilation_room_in_summer(month_data,hour_data):
    max_year = month_data['资料时间'].dt.year.max()
    min_year = month_data['资料时间'].dt.year.min()
    result = []
    for year in range(min_year, max_year + 1):
        data_year = month_data[month_data['资料时间'].dt.year == year]
        max_T_data = data_year[data_year['资料时间'].dt.year == year]['平均气温'].max()
        max_T_date = data_year[data_year['平均气温'] == max_T_data]['资料时间'].values[0]
        max_T_date = pd.to_datetime(max_T_date).month
        max_T_data_hour = hour_data[(hour_data['资料时间'].dt.year == year) & (hour_data['资料时间'].dt.month == max_T_date) & (hour_data['资料时间'].dt.hour ==  6)]['温度/气温'].mean()
        result.append(max_T_data_hour)
    result = np.array(result).mean()
    return round_data(result,1)


# 夏季通风室外计算相对湿度
def The_relative_humidity_is_calculated_outside_the_ventilation_room_in_summer(data_month,data_hour):
    max_year = data_month['资料时间'].dt.year.max()
    min_year = data_month['资料时间'].dt.year.min()
    result = []
    for year in range(min_year, max_year + 1):
        data_year = data_month[data_month['资料时间'].dt.year == year]
        max_T_data = data_year[data_year['资料时间'].dt.year == year]['平均气温'].max()
        max_T_date = data_year[data_year['平均气温'] == max_T_data]['资料时间'].values[0]
        max_T_date = pd.to_datetime(max_T_date).month
        max_T_data_hour = data_hour[(data_hour['资料时间'].dt.year == year) & (data_hour['资料时间'].dt.month == max_T_date) & (data_hour['资料时间'].dt.hour == 6)]['相对湿度'].mean()
        result.append(max_T_data_hour)
    result = np.array(result).mean()
    return round_data(result,1)

# 夏季空调室外计算日平均温度
def The_average_temperature_is_calculated_outside_the_air_conditioner_in_summer(data,values):
    daily_temperature = data['平均气温'].sort_values(ascending=False)
    value = calculate_value(data,values)
    temperature = daily_temperature.values[value]
    return round_data(temperature,1)


# %%

def main(start_date,end_date):
    print('供暖计算室外温度:',Heating_calculates_the_outside_temperature(Extract_data(start_date, end_date,'day_data'),5))
    print('冬季通风室外计算温度:',Calculate_the_temperature_outside_the_ventilation_room_in_winter(Extract_data(start_date, end_date,'month_data'),Extract_data(start_date, end_date,'day_data')))
    print('冬季空调室外计算温度:',In_winter_the_air_conditioner_calculates_the_temperature_outside(Extract_data(start_date, end_date,'day_data')))
    print('冬季空调室外计算相对湿度:',Relative_humidity_is_calculated_outside_the_air_conditioner_in_winter(Extract_data(start_date, end_date,'month_data'),Extract_data(start_date, end_date,'day_data')))
    #print('夏季空调室外计算干球温度:',In_summer_the_air_conditioner_calculates_the_dry_bulb_temperature_outside(Extract_data(start_date, end_date,'hour_data'),50))
    print('夏季通风室外计算温度:',The_temperature_is_calculated_outside_the_ventilation_room_in_summer(Extract_data(start_date, end_date,'month_data'),Extract_data(start_date, end_date,'hour_data')))
    print('夏季通风室外计算相对湿度:',The_relative_humidity_is_calculated_outside_the_ventilation_room_in_summer(Extract_data(start_date, end_date,'month_data'),Extract_data(start_date, end_date,'hour_data')))
    print('夏季空调室外计算日平均温度:',The_average_temperature_is_calculated_outside_the_air_conditioner_in_summer(Extract_data(start_date, end_date,'day_data'),5))


if __name__ == '__main__':
    start_date = '2021-01-01'  
    end_date = '2021-12-31' 
    main(start_date,end_date)


# %%
