import pandas as pd
from scipy.optimize import curve_fit
from scipy.optimize import fsolve
import numpy as np

# 拟合 序列 y， 并计算 wakeby 分布参数 （线性拟合）
def MT_wakebypara(y):
    def sfunc(xn, yb, alpha, beta, gamma, delta):
        x = xn
        fx = yb + alpha/beta*( 1-(1-x)**beta ) - gamma/delta*( 1-(1-x)**(-1*delta) )
        return fx
    y.sort()
    xn = np.arange(1,len(y)+1)/(1+len(y))
    popt, _ = curve_fit(sfunc, xn, y, maxfev=20000)
    yb, alpha, beta, gamma, delta = popt
    yori = y
    yest = yb + alpha / beta * (1 - (1 - xn) ** beta) - gamma / delta * (1 - (1 - xn) ** (-1 * delta))
    return yb, alpha, beta, gamma, delta, yori, yest


# 计算 单个值 yuni 的累计概率
def MT_wakebydisuni(y,yuni):
    yb, alpha, beta, gamma, delta, _, _ = MT_wakebypara(y)
    def fun(f):
        equ = yuni - (yb + alpha / beta * (1 - (1 - f) ** beta) - gamma / delta * (1 - (1 - f) ** (-1 * delta)))
        return equ
    f0 = 1e-5
    ff = fsolve(fun, f0)
    return ff


# 计算序列 ymul 的累计概率分布
def MT_wakebydismul(y,ymul):
    ffmul = []
    for i in ymul:
        ffmul.append(MT_wakebydisuni(y,i))
    return np.array(ffmul).reshape(len(ymul))


def MT_wakebysyn(y,hn=15,bins1=50, bins2=50, p=[2,5,10,20,30,50,100]):
    yb, alpha, beta, gamma, delta, ori, nihe = MT_wakebypara(y)
    hist, bin_edges = np.histogram(y, bins=hn, range=None, density=True)
    xbins1 = np.linspace(y.min(), y.max(), bins1+1)
    mm = MT_wakebydismul(y,xbins1)
    cdf = mm[1:]
    pdf = np.zeros(cdf.shape)
    pdf[:] = np.diff(mm,n=1)

    pv2 = np.linspace(0.001, 0.999, bins2)
    cxq2 = 1/pv2
    jz2 = np.zeros(pv2.shape)
    jz2 = yb + alpha / beta * (1 - pv2 ** beta) - gamma / delta * (1 - pv2 ** (-1 * delta))

    p = np.array(p)
    pv3 = 1/p
    jz3 = np.zeros(p.shape)
    jz3 = yb + alpha / beta * (1 - pv3 ** beta) - gamma / delta * (1 - pv3 ** (-1 * delta))

    return yb, alpha, beta, gamma, delta, ori, nihe, hist, bin_edges, xbins1, pdf, cdf, pv2, cxq2, jz2, jz3




file_path = r'D:\python\Return_period_extremum\test_data\1991~2020年德阳降水年际变化统计图.xlsx'
T = pd.read_excel(file_path)

precip = T['降水']
y = np.array(precip)

yb, alpha, beta, gamma, delta, ori, nihe, hist, bin_edges, xbins1, pdf, cdf, pv2, cxq2, jz2, jz3 = MT_wakebysyn(y)
print(yb, alpha, beta, gamma, delta)
print(jz3)

with open(r"D:\python\Return_period_extremum\End_Result\wakeby.txt", "w", encoding='utf-8') as file:
    file.write(f"位置参数 = {yb:.3f}, 尺度参数 = {alpha:.3f}，{gamma:.3f}, 形状参数 = {beta:.3f}，{delta:.3f}\n")
    p = [2,5,10,20,30,50,100]
    for i in range(len(jz3)):
        print(f"{p[i]}-year return level: {jz3[i]:.2f} mm")
        file.write(f"{p[i]}-year return level: {jz3[i]:.1f} mm\n")

