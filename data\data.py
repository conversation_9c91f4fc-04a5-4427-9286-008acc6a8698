#%%
import pandas as pd

data1 = pd.read_excel(r'D:\Data verification\data\SURF_CHN_MUL_DAY(1991-2000).xlsx')
data2 = pd.read_excel(r'D:\Data verification\data\SURF_CHN_MUL_DAY(2001-2010).xlsx')
data3 = pd.read_excel(r'D:\Data verification\data\SURF_CHN_MUL_DAY(2011-2020).xlsx')
data4 = pd.read_excel(r'D:\Data verification\data\SURF_CHN_MUL_DAY(2021-2025).xlsx')

#%%
# 拼接数据集，并按照日期排序
data1 = data1.sort_values(by='资料时间')
data2 = data2.sort_values(by='资料时间')
data3 = data3.sort_values(by='资料时间')
data4 = data4.sort_values(by='资料时间')

#%% 
#重命名
data1 = data1.rename(columns={'资料时间': 'time', '区站号(字符)': 'station'})
data2 = data2.rename(columns={'资料时间': 'time', '区站号(字符)': 'station'})
data3 = data3.rename(columns={'资料时间': 'time', '区站号(字符)': 'station'})
data4 = data4.rename(columns={'资料时间': 'time', '区站号(字符)':  'station'})

# %%
import xarray as xr
# Combine all dataframes
combined_data = pd.concat([data1, data2, data3, data4])

# Select the columns you want
selected_columns = ['station', '站名', 'time', '平均气温', '平均相对湿度', 
                   '20-20时降水量', '平均2分钟风速', '最大风速', '极大风速']

data = combined_data[selected_columns]

# Convert to xarray Dataset
# First, we need to set the station ID and time as coordinates
ds = xr.Dataset.from_dataframe(data.set_index(['station', 'time']))

# Rename variables to more standard names (optional)
ds = ds.rename({
    '站名': 'station_name',
    '平均气温': 'mean_temperature',
    '平均相对湿度': 'mean_relative_humidity',
    '20-20时降水量': 'precipitation',
    '平均2分钟风速': 'mean_wind_speed',
    '最大风速': 'max_wind_speed',
    '极大风速': 'extreme_wind_speed'
})

# Add some metadata (optional but recommended)
ds.attrs['title'] = 'China Meteorological Data'
ds.attrs['source'] = 'Original Excel files from SURF_CHN_MUL_DAY datasets'
ds.attrs['history'] = f'Created on {pd.Timestamp.now()}'

# Save to NetCDF file
output_path = r'D:\Data verification\data\CHN_meteo_data.nc'
ds.to_netcdf(output_path)

print(f"Data successfully saved to {output_path}")
# %%
import xarray as xr

# Load the NetCDF file
ds = xr.open_dataset(r'D:\Data verification\data\CHN_meteo_data.nc')
print(ds)
#%%

t = ds['mean_temperature']

# 选择指定的时间范围
start_date = '1991-01-01'
end_date = '2000-12-31'
t = t.sel(time=slice(start_date, end_date))
print(t)


# %%
