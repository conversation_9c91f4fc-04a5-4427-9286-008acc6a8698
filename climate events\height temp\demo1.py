#%%
import pandas as pd
import numpy as np

filepath = r'D:\Data verification\climate events\height temp\data\20-24最高气温.xlsx'
data = pd.read_excel(filepath)
data = data.sort_values(by='资料时间')

# 数据质量控制：处理缺测和异常值
print('开始数据质量控制...')
print(f'原始数据量: {len(data)}')

# 1. 处理缺测值
missing_values = [999999, 32700, 30000, -999, 9999]
data.loc[data['最高气温'].isin(missing_values), '最高气温'] = np.nan
# 2. 处理异常大值
data.loc[data['最高气温'] >= 10000, '最高气温'] = np.nan
# 3. 处理负值
data.loc[data['最高气温'] < 0, '最高气温'] = np.nan


# 选项2：用0填充缺测值
data['最高气温'] = data['最高气温'].fillna(0)

print(f'质控后数据量: {len(data)}')
print(f'最高气温范围: {data["最高气温"].min():.1f} - {data["最高气温"].max():.1f} ℃')
print('数据质量控制完成...')
print('资料读取完成...')

#print(data)
#%%
import numpy as np
from tqdm import tqdm

station_info = pd.read_excel(r'D:\Data verification\climate events\rainstorm\data\station_info.xlsx')
stations = station_info[['站号', '纬度', '经度']].values

# 经纬度计算距离 - 使用指定公式(1)
def distance(lat1, lon1, lat2, lon2):
    """
    使用指定公式计算两个经纬度点之间的距离（公里）
    公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180

    参数：
    lat1, lon1: 站点A的纬度和经度
    lat2, lon2: 站点B的纬度和经度

    返回：距离（公里）
    """
    import math

    # 地球平均半径，取6371公里；π=3.14
    R = 6371
    pi = 3.14

    # 将经纬度转换为弧度
    '''    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    lon1_rad = math.radians(lon1)
    lon2_rad = math.radians(lon2)'''

    # 应用公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180
    cos_angle = (math.sin(lat1) * math.sin(lat2) +
                 math.cos(lat1) * math.cos(lat2) *
                 math.cos(lon1 - lon2))

    # 防止数值误差导致的域错误（arccos的定义域是[-1,1]）
    cos_angle = max(-1, min(1, cos_angle))

    # 计算距离
    distance_km = R * math.acos(cos_angle) * pi / 180

    return distance_km


# 计算所有站点之间的距离矩阵
print("正在计算站点间距离...")
print("使用公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180")
print("其中：R = 6371公里，π = 3.14")
distance_matrix = np.zeros((len(stations), len(stations)))
for i, (id1, lat1, lon1) in tqdm(enumerate(stations)):
    for j, (id2, lat2, lon2) in enumerate(stations):
        if i < j:
            dist = distance(lat1, lon1, lat2, lon2)
            distance_matrix[i][j] = dist
            distance_matrix[j][i] = dist

# 构建相邻站点字典（300km）
adjacent_stations = {}
threshold = 300 
for i, (id1, _, _) in enumerate(stations):
    adjacent = stations[distance_matrix[i] <= threshold, 0].tolist()
    adjacent.remove(id1)
    adjacent_stations[id1] = adjacent

print("相邻站点构建完成。")


# 2. 构建监测区域（所有站点组成一个大区域）
all_stations = station_info['站号'].unique().tolist()
print(f"监测区域包含{len(all_stations)}个站点。")

#%%

# 3. 预处理气温数据
print('预处理气温数据...')

data_regional = data[['区站号(字符)', '资料时间', '最高气温']].copy()
# 确保时间列是 datetime 类型
data_regional['资料时间'] = pd.to_datetime(data_regional['资料时间'])



# 根据站点类型设置不同的高温标准
def classify_rainstorm_level(row):
    """根据气温量分类高温等级"""
    precipitation = row['最高气温']
    if precipitation >= 39:
        return '严重高温'
    elif precipitation >= 37:
        return '中度高温'
    elif precipitation >= 35:
        return '轻度高温'
    else:
        return '无高温'


# 应用分类函数
data_regional['高温等级'] = data_regional.apply(classify_rainstorm_level, axis=1)
data_regional['高温日'] = data_regional['高温等级'] != '无高温'

print('预处理完成。')

# %%

# 4. 计算每日区域高温情况
def find_drought_clusters(drought_stations, adjacent_stations):
    """使用深度优先搜索找到所有连通的高温站点群组"""
    visited = set()
    clusters = []

    def dfs(station, current_cluster):
        """深度优先搜索，找到连通的高温站点"""
        if station in visited or station not in drought_stations:
            return
        visited.add(station)
        current_cluster.add(station)

        # 遍历所有相邻站点
        for neighbor in adjacent_stations.get(station, []):
            if neighbor in drought_stations and neighbor not in visited:
                dfs(neighbor, current_cluster)

    # 对每个未访问的高温站点进行DFS
    for station in drought_stations:
        if station not in visited:
            cluster = set()
            dfs(station, cluster)
            if len(cluster) >= 2:  # 至少2个相邻站点才算群组
                clusters.append(list(cluster))

    return clusters

print("开始计算每日区域高温情况...")
daily_stats = []
for date in tqdm(data_regional['资料时间'].unique()):
    daily_data = data_regional[data_regional['资料时间'] == date]
    # 获取当日有高温的站点
    drought_stations = daily_data[daily_data['高温日']]['区站号(字符)'].unique().tolist()
    # 找到所有连通的高温站点群组
    drought_clusters = find_drought_clusters(drought_stations, adjacent_stations)
    # 选择最大的群组作为代表
    if drought_clusters:
        best_group = max(drought_clusters, key=len)
        cluster_count = len(drought_clusters)
    else:
        best_group = []
        cluster_count = 0

    # 判断是否达到20%阈值,31个
    is_regional_day = len(best_group)  >= 31

    daily_stats.append({
        '日期': date,
        '高温站点数': len(drought_stations),
        '有效群组站点数': len(best_group),
        '群组总数': cluster_count,
        '是否区域高温日': is_regional_day,
        '高温站点列表': best_group,
        '所有群组': drought_clusters 
    })

daily_df = pd.DataFrame(daily_stats)
print("每日区域高温情况计算完成。")
print(f"总共处理了 {len(daily_df)} 天的数据")
print(f"其中区域高温日有 {daily_df['是否区域高温日'].sum()} 天")

#%%

#中间过程数据输出
daily_df.to_csv(r'D:\Data verification\climate events\height temp\data\during_data.csv', index=False)

#%% 5. 区域性高温过程识别

print("开始进行区域性高温过程识别...")

# 5.1 重新处理每日数据，识别区域性组群
def identify_regional_clusters(clusters):
    """识别区域性组群（站点数占总站点数≥20%）"""
    regional_clusters = []
    for cluster in clusters:
        if len(cluster)  >= 31:
            regional_clusters.append(cluster)
    return regional_clusters

# 5.2 计算组群重合度
def calculate_overlap_rate(prev_cluster, current_cluster):
    """计算当前组群与前一日组群的重合度"""
    if not prev_cluster or not current_cluster:
        return 0.0

    prev_set = set(prev_cluster)
    current_set = set(current_cluster)
    overlap = len(prev_set & current_set)
    return overlap / len(current_set)

# 初始化新列
print("初始化新列...")
daily_df['区域性组群'] = [[] for _ in range(len(daily_df))]
daily_df['前一日区域性组群'] = [[] for _ in range(len(daily_df))]
daily_df['是否持续'] = False
daily_df['过程ID'] = ''

# 重新处理每日数据，添加区域性组群信息
print("重新处理每日数据，识别区域性组群...")
for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    all_clusters = row['所有群组']

    # 识别区域性组群
    regional_clusters = identify_regional_clusters(all_clusters)
    daily_df.at[i, '区域性组群'] = regional_clusters

    # 计算与前一日的重合度
    if i > 0:
        prev_regional = daily_df.iloc[i-1]['区域性组群']
        daily_df.at[i, '前一日区域性组群'] = prev_regional

        # 如果当前和前一日都有区域性组群，计算重合度
        if regional_clusters and prev_regional:
            # 选择最大的区域性组群进行比较
            current_main = max(regional_clusters, key=len)
            prev_main = max(prev_regional, key=len)
            overlap_rate = calculate_overlap_rate(prev_main, current_main)

            # 判断是否持续（重合度≥50%）
            is_continuous = overlap_rate >= 0
            daily_df.at[i, '是否持续'] = is_continuous
        else:
            daily_df.at[i, '是否持续'] = False
    else:
        daily_df.at[i, '前一日区域性组群'] = []
        daily_df.at[i, '是否持续'] = False

print("区域性组群识别完成。")

# 5.3 区域性高温过程识别
print("开始识别区域性高温过程...")

processes = []
current_process = None
process_id = 1

for i in tqdm(range(len(daily_df))):
    row = daily_df.iloc[i]
    has_regional_cluster = len(row['区域性组群']) > 0

    if current_process is None:
        # 开始新过程：当日有区域性组群
        if has_regional_cluster:
            current_process = {
                'process_id': process_id,
                'start_date': row['日期'],
                'end_date': row['日期'],
                'duration': 1,
                'dates': [row['日期']],
                'total_rainstorm_stations': len(row['高温站点列表']),
                'regional_clusters': [row['区域性组群']]
            }
            daily_df.at[i, '过程ID'] = process_id
    else:
        # 过程持续中
        if has_regional_cluster and row['是否持续']:
            # 过程继续
            current_process['end_date'] = row['日期']
            current_process['duration'] += 1
            current_process['dates'].append(row['日期'])
            current_process['total_rainstorm_stations'] += len(row['高温站点列表'])
            current_process['regional_clusters'].append(row['区域性组群'])
            daily_df.at[i, '过程ID'] = current_process['process_id']
        else:
            # 过程结束
            processes.append(current_process)
            process_id += 1

            # 检查是否立即开始新过程
            if has_regional_cluster:
                current_process = {
                    'process_id': process_id,
                    'start_date': row['日期'],
                    'end_date': row['日期'],
                    'duration': 1,
                    'dates': [row['日期']],
                    'total_rainstorm_stations': len(row['高温站点列表']),
                    'regional_clusters': [row['区域性组群']]
                }
                daily_df.at[i, '过程ID'] = process_id
            else:
                current_process = None
                daily_df.at[i, '过程ID'] = ''

# 处理最后一个未结束的过程
if current_process:
    processes.append(current_process)

print(f"区域性高温过程识别完成，共识别出 {len(processes)} 个过程。")

# 5.4 计算过程统计指标
print("计算过程统计指标...")

def calculate_process_statistics(process, data_regional, all_stations):
    """计算单个过程的详细统计指标"""
    start_date = process['start_date']
    end_date = process['end_date']
    duration = process['duration']

    # 确保日期是 datetime 对象
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)

    # 确保 data_regional 中的时间列也是 datetime 类型
    if not pd.api.types.is_datetime64_any_dtype(data_regional['资料时间']):
        data_regional['资料时间'] = pd.to_datetime(data_regional['资料时间'])

    # 获取过程期间的数据
    mask = (data_regional['资料时间'] >= start_date) & (data_regional['资料时间'] <= end_date)
    process_data = data_regional[mask]

    # 计算影响范围（过程期间所有发生高温的站点）
    affected_stations = set()
    daily_stats = []

    for date in process['dates']:
        # 确保日期是 datetime 对象
        if isinstance(date, str):
            date = pd.to_datetime(date)
        daily_data = data_regional[data_regional['资料时间'] == date]

        # 统计各等级高温站数（根据站点类型使用不同标准）
        rainstorm_stations = daily_data[daily_data['高温等级'] == '轻度高温']
        heavy_rainstorm_stations = daily_data[daily_data['高温等级'] == '中度高温']
        severe_rainstorm_stations = daily_data[daily_data['高温等级'] == '严重高温']
        all_rainstorm_stations = daily_data[daily_data['高温日']]

        # 获取当日最大气温的站名
        if len(all_rainstorm_stations) > 0:
            max_idx = all_rainstorm_stations['最高气温'].idxmax()
            max_station_id = all_rainstorm_stations.loc[max_idx, '区站号(字符)']
            # 从原始数据中查找对应的站名
            station_name_match = data[data['区站号(字符)'] == max_station_id]['站名'].iloc[0] if len(data[data['区站号(字符)'] == max_station_id]) > 0 else max_station_id
            max_daily_precip = all_rainstorm_stations['最高气温'].max()
        else:
            station_name_match = ''
            max_daily_precip = 0

        daily_stat = {
            'date': date,
            'rainstorm_count': len(rainstorm_stations),
            'heavy_rainstorm_count': len(heavy_rainstorm_stations),
            'severe_rainstorm_count': len(severe_rainstorm_stations),
            'max_precipitation': max_daily_precip,
            'max_precip_station': station_name_match
        }
        daily_stats.append(daily_stat)

        # 更新影响站点
        daily_rainstorm_stations = all_rainstorm_stations['区站号(字符)'].unique()
        affected_stations.update(daily_rainstorm_stations)

    # 计算平均影响范围
    total_daily_regional_stations = 0

    for date in process['dates']:
        # 确保日期是 datetime 对象
        if isinstance(date, str):
            date = pd.to_datetime(date)
        daily_data = data_regional[data_regional['资料时间'] == date]

        # 获取当日高温站点
        daily_rainstorm_stations = daily_data[daily_data['高温日']]['区站号(字符)'].unique().tolist()

        if len(daily_rainstorm_stations)  >= 31: 
            daily_regional_count = len(daily_rainstorm_stations)
        else:
            daily_regional_count = 0

        total_daily_regional_stations += daily_regional_count

    # 平均影响范围 = 总的日区域性组群站点数 / 持续天数
    avg_influence_range = total_daily_regional_stations / duration if duration > 0 else 0
    
    # 计算平均强度（过程期间所有高温站点的平均气温量）
    rainstorm_data = process_data[process_data['高温日']]
    avg_intensity = rainstorm_data['最高气温'].mean() if len(rainstorm_data) > 0 else 0

    # 计算综合强度 - 使用公式(4): Z = Ia × Aa^0.5 × T^0.5
    # Ia: 区域性高温过程平均强度
    # Aa: 区域性高温过程平均范围（站点数）
    # T: 区域性高温过程持续时间长度
    comprehensive_intensity = avg_intensity * (avg_influence_range ** 0.5) * (duration ** 0.5)

    # 确定等级（基于综合强度）
    if comprehensive_intensity >= 100:
        level = "特强"
    elif comprehensive_intensity >= 50:
        level = "强"
    elif comprehensive_intensity >= 20:
        level = "中等"
    else:
        level = "弱"

    # 找出整个过程中所有站点的最大和次大气温量
    # 获取过程期间所有高温站点的气温记录，并合并站名信息
    all_rainstorm_records = process_data[process_data['高温日']].copy()

    # 从原始数据中获取站名信息
    station_names = data[['区站号(字符)', '站名']].drop_duplicates()
    all_rainstorm_records = all_rainstorm_records.merge(station_names, on='区站号(字符)', how='left')

    if len(all_rainstorm_records) > 0:
        # 按气温量排序，找出最大和次大值
        sorted_records = all_rainstorm_records.sort_values('最高气温', ascending=False)

        # 最大气温
        max_record = sorted_records.iloc[0]
        max_precip = {
            'max_precipitation': max_record['最高气温'],
            'date': max_record['资料时间'],
            'max_precip_station': max_record['站名'] if pd.notna(max_record['站名']) else max_record['区站号(字符)']
        }

        # 次大气温
        if len(sorted_records) > 1:
            second_record = sorted_records.iloc[1]
            second_max_precip = {
                'max_precipitation': second_record['最高气温'],
                'date': second_record['资料时间'],
                'max_precip_station': second_record['站名'] if pd.notna(second_record['站名']) else second_record['区站号(字符)']
            }
        else:
            second_max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}
    else:
        max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}
        second_max_precip = {'max_precipitation': 0, 'date': '', 'max_precip_station': ''}

    # 找出最多高温站数的日期
    max_rainstorm_day = max(daily_stats, key=lambda x: x['rainstorm_count']) if daily_stats else {'rainstorm_count': 0, 'date': ''}

    # 统计总站数
    total_rainstorm_stations = sum([d['rainstorm_count'] for d in daily_stats])
    total_heavy_rainstorm_stations = sum([d['heavy_rainstorm_count'] for d in daily_stats])
    total_severe_rainstorm_stations = sum([d['severe_rainstorm_count'] for d in daily_stats])

    return {
        '开始时间': start_date.strftime('%Y-%m-%d'),
        '结束时间': end_date.strftime('%Y-%m-%d'),
        '持续时间': duration,
        '平均强度': round(avg_intensity, 1),
        '平均影响范围': round(avg_influence_range, 1),
        '综合强度': round(comprehensive_intensity, 2),
        '等级': level,
        '最大日最高气温': round(max_precip['max_precipitation'], 1),
        '最大日最高气温出现日期': max_precip['date'].strftime('%Y-%m-%d') if hasattr(max_precip['date'], 'strftime') else str(max_precip['date']),
        '最大日最高气温出现站': max_precip['max_precip_station'],
        '次大日最高气温': round(second_max_precip['max_precipitation'], 1),
        '次大日最高气温出现日期': second_max_precip['date'].strftime('%Y-%m-%d') if hasattr(second_max_precip['date'], 'strftime') else str(second_max_precip['date']),
        '次大日最高气温出现站': second_max_precip['max_precip_station'],
        '最多高温站数': max_rainstorm_day['rainstorm_count'],
        '最多高温站数出现日期': max_rainstorm_day['date'].strftime('%Y-%m-%d') if hasattr(max_rainstorm_day['date'], 'strftime') else str(max_rainstorm_day['date']),
        '轻度高温站数': total_rainstorm_stations,
        '中度高温站数': total_heavy_rainstorm_stations,
        '严重高温站数': total_severe_rainstorm_stations
    }

# 生成过程统计表
print("生成过程统计表...")
process_stats = []
for process in processes:
    stats = calculate_process_statistics(process, data_regional, all_stations)
    process_stats.append(stats)

process_df = pd.DataFrame(process_stats)

# 保存过程统计表（严格按照指定格式）
process_output_path = r'D:\Data verification\climate events\height temp\data\区域性高温过程统计表.csv'
process_df.to_csv(process_output_path, index=False, encoding='utf-8-sig')
print(f"区域性高温过程统计表已保存至: {process_output_path}")


print("\n区域性高温过程识别完成！")

# %%

