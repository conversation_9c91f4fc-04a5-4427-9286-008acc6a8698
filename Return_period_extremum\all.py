import numpy as np
import pandas as pd
from scipy.stats import genextreme, gumbel_r, weibull_min, norm, pearson3, expon
from scipy.special import gamma
from scipy.optimize import curve_fit
from scipy.optimize import fsolve


def gev_fit(data, method='MLE'):
    """
    拟合GEV分布参数
    :param data: 年最大降水序列
    :param method: 参数估计方法('MLE'或'PWM')
    :return: 形状参数c, 位置参数loc, 尺度参数scale
    """
    if method == 'MLE':
        # 最大似然估计
        params = genextreme.fit(data)
        c = params[0]  # 形状参数(注意scipy中c = -ξ)
        loc = params[1]
        scale = params[2]
    elif method == 'PWM':
        # 概率权重矩估计(需要自己实现)
        c, loc, scale = gev_fit_pwm(data)
    else:
        raise ValueError("Method must be 'MLE' or 'PWM'")

    return c, loc, scale


def gev_fit_pwm(data):
    """
    使用概率权重矩法(PWM)估计GEV参数
    """
    data_sorted = np.sort(data)
    n = len(data_sorted)

    # 计算概率权重矩
    b0 = np.mean(data_sorted)
    b1 = np.mean(data_sorted * (np.arange(1, n + 1) - 1) / (n - 1))
    b2 = np.mean(data_sorted * (np.arange(1, n + 1) - 1) * (np.arange(1, n + 1) - 2) / ((n - 1) * (n - 2)))

    # 估计参数
    c = 7.8590 * (2 * b1 - b0) / (3 * b1 - b0) - 2.9554 * (2 * b1 - b0) / (3 * b1 - b0) ** 2
    scale = (2 * b1 - b0) * c / (gamma(1 + c) * (1 - 2 ** (-c)))
    loc = b0 - scale * (1 - gamma(1 + c)) / c

    return -c, loc, scale  # 注意scipy中形状参数c = -ξ


def calculate_gev_return_level(c, loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param c: 形状参数(注意scipy中c = -ξ)
    :param loc: 位置参数
    :param scale: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return genextreme.ppf(non_exceedance_prob, c, loc=loc, scale=scale)


def gumbel_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = gumbel_r.fit(data)
    return params[0], params[1]  # mu, beta


def calculate_gumbel_return_level(mu, beta, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 位置参数
    :param beta: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return gumbel_r.ppf(non_exceedance_prob, loc=mu, scale=beta)


def weibull_fit(data):
    """
    使用最大似然估计拟合韦布尔分布参数
    :param data: 年最大降水序列
    :return: 形状参数c, 位置参数loc ,尺度参数scale
    """
    c, loc, scale = weibull_min.fit(data)
    return c, loc, scale


def calculate_weibull_return_level(c, loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param k: 形状参数
    :param λ: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return weibull_min.ppf(non_exceedance_prob, c, loc=loc, scale=scale)


# 拟合 序列 y， 并计算 wakeby 分布参数 （线性拟合）
def MT_wakebypara(y):
    def sfunc(xn, yb, alpha, beta, gamma, delta):
        x = xn
        fx = yb + alpha / beta * (1 - (1 - x) ** beta) - gamma / delta * (1 - (1 - x) ** (-1 * delta))
        return fx

    y.sort()
    xn = np.arange(1, len(y) + 1) / (1 + len(y))
    popt, _ = curve_fit(sfunc, xn, y, maxfev=20000)
    yb, alpha, beta, gamma, delta = popt
    yori = y
    yest = yb + alpha / beta * (1 - (1 - xn) ** beta) - gamma / delta * (1 - (1 - xn) ** (-1 * delta))
    return yb, alpha, beta, gamma, delta, yori, yest


# 计算 单个值 yuni 的累计概率
def MT_wakebydisuni(y, yuni):
    yb, alpha, beta, gamma, delta, _, _ = MT_wakebypara(y)

    def fun(f):
        equ = yuni - (yb + alpha / beta * (1 - (1 - f) ** beta) - gamma / delta * (1 - (1 - f) ** (-1 * delta)))
        return equ

    f0 = 1e-5
    ff = fsolve(fun, f0)
    return ff


# 计算序列 ymul 的累计概率分布
def MT_wakebydismul(y, ymul):
    ffmul = []
    for i in ymul:
        ffmul.append(MT_wakebydisuni(y, i))
    return np.array(ffmul).reshape(len(ymul))


def MT_wakebysyn(y, hn=15, bins1=50, bins2=50, p=[2, 5, 10, 20, 30, 50, 100]):
    yb, alpha, beta, gamma, delta, ori, nihe = MT_wakebypara(y)
    hist, bin_edges = np.histogram(y, bins=hn, range=None, density=True)
    xbins1 = np.linspace(y.min(), y.max(), bins1 + 1)
    mm = MT_wakebydismul(y, xbins1)
    cdf = mm[1:]
    pdf = np.zeros(cdf.shape)
    pdf[:] = np.diff(mm, n=1)

    pv2 = np.linspace(0.001, 0.999, bins2)
    cxq2 = 1 / pv2
    jz2 = np.zeros(pv2.shape)
    jz2 = yb + alpha / beta * (1 - pv2 ** beta) - gamma / delta * (1 - pv2 ** (-1 * delta))

    p = np.array(p)
    pv3 = 1 / p
    jz3 = np.zeros(p.shape)
    jz3 = yb + alpha / beta * (1 - pv3 ** beta) - gamma / delta * (1 - pv3 ** (-1 * delta))

    return yb, alpha, beta, gamma, delta, ori, nihe, hist, bin_edges, xbins1, pdf, cdf, pv2, cxq2, jz2, jz3


def normal_mle_fit(data):
    """
    使用最大似然估计拟合正态分布参数
    :param data: 年最大降水序列
    :return: 均值mu, 标准差sigma
    """
    mu = np.mean(data)
    sigma = np.std(data)
    return mu, sigma


def calculate_normal_return_level(mu, sigma, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 均值
    :param sigma: 标准差
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return norm.ppf(non_exceedance_prob, loc=mu, scale=sigma)


def pearson3_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = pearson3.fit(data)
    return params[0], params[1], params[2]


def calculate_pearson3_return_level(shape, loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param shape: 形状参数
    :param loc: 位置参数
    :param scale: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return pearson3.ppf(non_exceedance_prob, shape, loc=loc, scale=scale)


def exponential_fit(data):
    """
    使用最大似然估计拟合指数分布参数
    :param data: 年最大降水序列
    :return: 尺度参数scale (1/λ)
    """
    # 指数分布的MLE估计就是样本均值
    loc, scale = expon.fit(data)
    return loc, scale


def calculate_exponential_return_level(loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param loc: 位置参数
    :param scale: 尺度参数 (1/λ)
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return expon.ppf(non_exceedance_prob, loc=loc, scale=scale)


# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path1 = r'D:\python\Return_period_extremum\test_data\1991~2020年德阳降水年际变化统计图.xlsx'
    T1 = pd.read_excel(file_path1)

    file_path2 = r'D:\python\Return_period_extremum\Bureau_data\56247_1440min.txt'
    T2 = pd.read_csv(file_path2, sep='\t',engine='python').values

    #precip = T1['降水']
    precip = T2[:,1]

    return_periods = [2, 5, 10, 20, 30, 50, 100]

    with open(r"D:\python\Return_period_extremum\End_Result\results2.txt", "w", encoding='utf-8') as file:
        # GEV分布
        c, loc, scale = gev_fit(precip, method='MLE')
        file.write("GEV分布:\n")
        file.write(f"形状参数 = {c:.3f}, 位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f} \n")
        for rp in return_periods:
            rl = calculate_gev_return_level(c, loc, scale, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
        file.write("\n")

        # 耿贝尔分布
        mu, beta = gumbel_mle_fit(precip)
        file.write("耿贝尔分布:\n")
        file.write(f"位置参数 = {mu:.3f}, 尺度参数 = {beta:.3f} \n")
        for rp in return_periods:
            rl = calculate_gumbel_return_level(mu, beta, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
        file.write("\n")

        # 韦布尔分布
        c, loc, scale = weibull_fit(precip)
        file.write("韦布尔分布:\n")
        file.write(f"形状参数 = {c:.3f}, 位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f} \n")
        for rp in return_periods:
            rl = calculate_weibull_return_level(c, loc, scale, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
        file.write("\n")

        # Wakeby分布
        y = np.array(precip)
        yb, alpha, beta, gamma, delta, _, _, _, _, _, _, _, _, _, _, jz3 = MT_wakebysyn(y)
        file.write("Wakeby分布:\n")
        file.write(f"位置参数 = {yb:.3f}, 尺度参数 = {alpha:.3f}，{gamma:.3f}, 形状参数 = {beta:.3f}，{delta:.3f}\n")
        for i in range(len(jz3)):
            rp = return_periods[i]
            file.write(f"{rp}-year return level: {jz3[i]:.1f} mm\n")
        file.write("\n")

        # 正态分布
        mu, sigma = normal_mle_fit(precip)
        file.write("正态分布:\n")
        file.write(f"位置参数 = {mu:.3f}, 尺度参数 = {sigma:.3f} \n")
        for rp in return_periods:
            rl = calculate_normal_return_level(mu, sigma, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
        file.write("\n")

        # Pearson3分布
        shape, loc, scale = pearson3_mle_fit(precip)
        file.write("Pearson3分布:\n")
        file.write(f"形状参数 = {shape:.3f} 位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f} \n")
        for rp in return_periods:
            rl = calculate_pearson3_return_level(shape, loc, scale, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
        file.write("\n")

        # 指数分布
        loc, scale = exponential_fit(precip)
        file.write("指数分布:\n")
        file.write(f"位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f} \n")
        for rp in return_periods:
            rl = calculate_exponential_return_level(loc, scale, rp)
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")