import pandas as pd
import numpy as np
from scipy.stats import gumbel_r



def gumbel_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = gumbel_r.fit(data)
    sigma = params[1] * np.pi / np.sqrt(6)  # 计算标准差
    return params[0], params[1], sigma  # mu, beta


def calculate_return_level(mu, beta, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 位置参数
    :param beta: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return gumbel_r.ppf(non_exceedance_prob, loc=mu, scale=beta)



if __name__ == "__main__":
    file_path = r'D:\Data verification\Return_period_extremum\重现期验证数据\56247_1440min_copy.txt'
    T = pd.read_csv(file_path, sep='\t').values
    precip = T[:,1]
    mu, beta, sigma = gumbel_mle_fit(precip)
    print(f"Estimated parameters: mu = {mu:.2f}, beta = {beta:.2f}, sigma = {sigma:.2f}")
    
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    for rp in return_periods:
        rl = calculate_return_level(mu, beta, rp)
        print(f"{rp}-year return level: {rl:.2f} mm")


    
    