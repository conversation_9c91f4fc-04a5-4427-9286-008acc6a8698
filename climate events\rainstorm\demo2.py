def distance(lat1, lon1, lat2, lon2):
    """
    使用指定公式计算两个经纬度点之间的距离（公里）
    公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180

    参数：
    lat1, lon1: 站点A的纬度和经度
    lat2, lon2: 站点B的纬度和经度

    返回：距离（公里）
    """
    import math

    # 地球平均半径，取6371公里；π=3.14
    R = 6371
    pi = 3.14

    # 将经纬度转换为弧度
    '''    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    lon1_rad = math.radians(lon1)
    lon2_rad = math.radians(lon2)'''

    # 应用公式(1)：D = R × arccos(sin(LatA) × sin(LatB) + cos(LatA) × cos(LatB) × cos(LonA - LonB)) × π/180
    '''    cos_angle = (math.sin(lat1_rad) * math.sin(lat2_rad) +
                 math.cos(lat1_rad) * math.cos(lat2_rad) *
                 math.cos(lon1_rad - lon2_rad))'''
    cos_angle = (math.sin(lat1) * math.sin(lat2) +
                 math.cos(lat1) * math.cos(lat2) *
                 math.cos(lon1 - lon2))

    # 防止数值误差导致的域错误（arccos的定义域是[-1,1]）
    cos_angle = max(-1, min(1, cos_angle))

    # 计算距离
    distance_km = R * math.acos(cos_angle) * pi / 180

    return distance_km

a = distance(31.0978, 107.8467, 31.0553, 106.5811)
print(a)
