
import pandas as pd
from scipy.stats import weibull_min

def weibull_fit(data):

    """
    使用最大似然估计拟合韦布尔分布参数
    :param data: 年最大降水序列
    :return: 形状参数c, 位置参数loc ,尺度参数scale
    """
    c, loc, scale = weibull_min.fit(data)
    return c, loc, scale


def calculate_weibull_return_level(c, loc, scale, return_period):

    """
    计算给定重现期的降水极值
    :param k: 形状参数
    :param λ: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return weibull_min.ppf(non_exceedance_prob, c, loc=loc, scale=scale)


# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path = r'D:\python\Return_period_extremum\Bureau_data\56247_1440min.txt'
    T = pd.read_csv(file_path,sep='\t',engine='python').values

    precip = T[:,1]

    # 拟合韦布尔分布(最大似然估计)
    c, loc, scale = weibull_fit(precip)
    print(f"形状参数 = {c:.3f}, 位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f}")
    
    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\Bureau_data\End_result\weibull.txt", "w", encoding='utf-8') as file:
        file.write(f"alpha = {c:.3f}, beta = {scale:.3f}, gamma = {loc:.3f}\n")
        for rp in return_periods:
            rl = calculate_weibull_return_level(c, loc, scale, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")



