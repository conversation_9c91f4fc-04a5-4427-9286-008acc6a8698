import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime, timedelta
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\SimHei.ttf")
except:
    print("未找到中文字体，图表中文可能无法正确显示")
    font = FontProperties()

# 数据路径
file_path = r"D:\Data verification\climate events\cold\data\2024-2025年156站最低气温.xlsx"

# 读取数据
def read_data(file_path):
    """
    读取气象站点最低气温数据
    """
    print("正在读取数据...")
    df = pd.read_excel(file_path)
    
    # 确保列名正确
    expected_columns = ['区站号(字符)', '站名', '资料时间', '最低气温']
    for col in expected_columns:
        if col not in df.columns:
            print(f"警告: 未找到列 '{col}'，可用列: {df.columns.tolist()}")
    
    # 确保资料时间列为日期时间格式
    df['资料时间'] = pd.to_datetime(df['资料时间'])
    
    # 按站点和日期排序
    df = df.sort_values(by=['区站号(字符)', '资料时间'])
    
    # 处理缺失值
    if df['最低气温'].isna().sum() > 0:
        print(f"警告: 发现 {df['最低气温'].isna().sum()} 条缺失的最低气温数据")
    
    print(f"数据读取完成，共 {len(df)} 条记录，{df['区站号(字符)'].nunique()} 个站点")
    return df

# 计算降温幅度
def calculate_temperature_drops(df):
    """
    计算24小时、48小时和72小时降温幅度
    """
    print("计算降温幅度...")
    result_df = pd.DataFrame()
    
    # 为每个站点单独计算
    for station, group in df.groupby('区站号(字符)'):
        # 按时间排序
        station_data = group.sort_values(by='资料时间')
        
        # 创建新的DataFrame保存结果
        station_result = station_data.copy()
        
        # 计算24小时降温幅度：后一日最低气温与当日最低气温之差的绝对值
        station_result['dT24'] = station_data['最低气温'].shift(-1) - station_data['最低气温']
        station_result['dT24'] = np.where(station_result['dT24'] < 0, 
                                         station_result['dT24'].abs(), 
                                         0)
        
        # 计算48小时降温幅度：后两日最低的日最低气温与当日日最低气温之差的绝对值
        min_temp_next_2days = pd.DataFrame({
            'min_next_2': pd.concat([
                station_data['最低气温'].shift(-1),
                station_data['最低气温'].shift(-2)
            ], axis=1).min(axis=1)
        })
        station_result['dT48'] = min_temp_next_2days['min_next_2'] - station_data['最低气温']
        station_result['dT48'] = np.where(station_result['dT48'] < 0, 
                                         station_result['dT48'].abs(), 
                                         0)
        
        # 计算72小时降温幅度：后三日最低的日最低气温与当日日最低气温之差的绝对值
        min_temp_next_3days = pd.DataFrame({
            'min_next_3': pd.concat([
                station_data['最低气温'].shift(-1),
                station_data['最低气温'].shift(-2),
                station_data['最低气温'].shift(-3)
            ], axis=1).min(axis=1)
        })
        station_result['dT72'] = min_temp_next_3days['min_next_3'] - station_data['最低气温']
        station_result['dT72'] = np.where(station_result['dT72'] < 0, 
                                         station_result['dT72'].abs(), 
                                         0)
        
        # 判断48小时内是否连续下降
        station_result['temp_next_1'] = station_data['最低气温'].shift(-1)
        station_result['temp_next_2'] = station_data['最低气温'].shift(-2)
        station_result['is_continuous_drop_48h'] = (
            (station_result['最低气温'] > station_result['temp_next_1']) & 
            (station_result['temp_next_1'] > station_result['temp_next_2'])
        )
        
        # 判断72小时内是否连续下降
        station_result['temp_next_3'] = station_data['最低气温'].shift(-3)
        station_result['is_continuous_drop_72h'] = (
            (station_result['最低气温'] > station_result['temp_next_1']) & 
            (station_result['temp_next_1'] > station_result['temp_next_2']) &
            (station_result['temp_next_2'] > station_result['temp_next_3'])
        )
        
        result_df = pd.concat([result_df, station_result])
    
    print("降温幅度计算完成")
    return result_df

# 判断单站冷空气等级
def determine_cold_air_level(df):
    """
    根据降温幅度和最低气温判断单站冷空气等级
    等级: 0-无冷空气, 1-中等强度冷空气, 2-强冷空气, 3-寒潮
    """
    print("判断单站冷空气等级...")
    
    # 创建冷空气等级列，默认为0（无冷空气）
    df['cold_air_level'] = 0
    
    # 1. 优先判断寒潮 (Level 3)
    # 寒潮: 满足三个条件之一且最低气温≤4℃
    # 1.1. 单站∆T24≥8℃
    mask_severe_1 = (df['dT24'] >= 8) & (df['temp_next_1'] <= 4)
    # 1.2. 单站∆T48≥10℃且48h是日最低气温必须是连续下降的
    mask_severe_2 = (
        (df['dT48'] >= 10) & 
        (df['is_continuous_drop_48h'] == True) & 
        (df['temp_next_2'] <= 4)
    )
    # 1.3. 单站∆T72≥12℃且72h是日最低气温必须是连续下降的
    mask_severe_3 = (
        (df['dT72'] >= 12) & 
        (df['is_continuous_drop_72h'] == True) & 
        (df['temp_next_3'] <= 4)
    )
    mask_severe = mask_severe_1 | mask_severe_2 | mask_severe_3
    df.loc[mask_severe, 'cold_air_level'] = 3
    
    # 2. 其次判断强冷空气 (Level 2)
    # 强冷空气: 单站∆T48≥8℃的冷空气, 且尚未被评为寒潮
    mask_strong = (df['dT48'] >= 8)
    df.loc[mask_strong & (df['cold_air_level'] == 0), 'cold_air_level'] = 2
    
    # 3. 最后判断中等强度冷空气 (Level 1)
    # 中等强度冷空气: 8℃>单站∆T48≥6℃的冷空气, 且尚未被评为更高等级
    mask_medium = (df['dT48'] >= 6) & (df['dT48'] < 8)
    df.loc[mask_medium & (df['cold_air_level'] == 0), 'cold_air_level'] = 1
    
    # 将冷空气等级映射为文字描述
    level_mapping = {
        0: '无冷空气',
        1: '中等强度冷空气',
        2: '强冷空气',
        3: '寒潮'
    }
    df['cold_air_level_name'] = df['cold_air_level'].map(level_mapping)
    
    print("单站冷空气等级判断完成")
    return df

# 识别区域冷空气过程
def identify_regional_cold_air_process(df, threshold_percentage=0.2):
    """
    识别区域冷空气过程
    """
    print("识别区域冷空气过程...")
    
    # 确定总站点数
    total_stations = df['区站号(字符)'].nunique()
    threshold_stations = total_stations * threshold_percentage
    print(f"总站点数: {total_stations}, 阈值站点数(20%): {threshold_stations}")
    
    # 按日期统计各等级站点数
    # 使用agg方法替代apply方法，避免DeprecationWarning
    daily_stats = df.groupby('资料时间').agg(
        medium_count=pd.NamedAgg(column='cold_air_level', aggfunc=lambda x: sum(x == 1)),
        strong_count=pd.NamedAgg(column='cold_air_level', aggfunc=lambda x: sum(x == 2)),
        severe_count=pd.NamedAgg(column='cold_air_level', aggfunc=lambda x: sum(x == 3)),
        total_affected=pd.NamedAgg(column='cold_air_level', aggfunc=lambda x: sum(x >= 1))
    ).reset_index()
    
    # 判断每天是否满足区域冷空气过程条件（≥20%站点出现中等及以上冷空气）
    daily_stats['is_process_day'] = daily_stats['total_affected'] >= threshold_stations
    
    # 初始化过程ID
    process_id = 0
    daily_stats['process_id'] = None
    
    # 找出所有可能的过程（连续两天及以上满足条件）
    in_process = False
    process_start_idx = 0
    
    for i in range(len(daily_stats)):
        if daily_stats.iloc[i]['is_process_day']:
            if not in_process:
                # 新过程开始
                in_process = True
                process_start_idx = i
        else:
            if in_process:
                # 过程结束，检查是否持续两天及以上
                if i - process_start_idx >= 2:
                    # 符合条件，给该过程分配ID
                    process_id += 1
                    daily_stats.iloc[process_start_idx:i, daily_stats.columns.get_loc('process_id')] = process_id
                in_process = False
    
    # 检查最后一个过程是否仍在进行中
    if in_process and len(daily_stats) - process_start_idx >= 2:
        process_id += 1
        daily_stats.iloc[process_start_idx:, daily_stats.columns.get_loc('process_id')] = process_id
    
    # 进一步细分过程（如果站点数先减少后增加）
    if process_id > 0:
        for pid in range(1, process_id + 1):
            process_data = daily_stats[daily_stats['process_id'] == pid].copy()
            
            if len(process_data) >= 3:  # 至少需要3天才能有先减少后增加的情况
                splits = []
                for j in range(1, len(process_data) - 1):
                    if (process_data.iloc[j]['total_affected'] < process_data.iloc[j-1]['total_affected'] and 
                        process_data.iloc[j]['total_affected'] < process_data.iloc[j+1]['total_affected']):
                        splits.append(j)
                
                # 如果需要分割，更新process_id
                if splits:
                    # 记录原始索引
                    original_indices = process_data.index.tolist()
                    
                    # 第一段保持原ID
                    new_id = process_id
                    for s in splits:
                        new_id += 1
                        # 后面的段落使用新ID
                        for idx in original_indices[s+1:]:
                            daily_stats.at[idx, 'process_id'] = new_id
                    
                    # 更新总process_id
                    process_id = new_id
    
    # 汇总每个过程的信息
    process_summary = []
    
    if process_id > 0:
        for pid in range(1, process_id + 1):
            process_data = daily_stats[daily_stats['process_id'] == pid]
            
            if len(process_data) >= 2:  # 确保至少持续两天
                start_date = process_data['资料时间'].min()
                end_date = process_data['资料时间'].max() + timedelta(days=1)  # 区域冷空气过程的结束日为站点数<20%的首日
                
                # 合并当前过程期间的站点数据，计算各站点在此过程中的最高等级
                process_stations_data = df[df['资料时间'].isin(process_data['资料时间'])]
                station_max_level = process_stations_data.groupby('区站号(字符)')['cold_air_level'].max()
                
                # 计算各等级的站点数
                n1 = sum(station_max_level == 1)  # 中等强度冷空气站点数
                n2 = sum(station_max_level == 2)  # 强冷空气站点数
                n3 = sum(station_max_level == 3)  # 寒潮站点数
                total_affected = n1 + n2 + n3
                
                # 计算强度指数 I
                intensity_index = (3*n3 + 2*n2 + n1) / total_affected if total_affected > 0 else 0
                
                # 确定强度等级
                intensity_level = '未达标'
                if 1.0 <= intensity_index < 1.7:
                    intensity_level = '中等强度冷空气过程'
                elif 1.7 <= intensity_index < 1.95:
                    intensity_level = '强冷空气过程'
                elif 1.95 <= intensity_index < 3:
                    intensity_level = '寒潮过程'
                
                # 计算综合强度指数 M
                comprehensive_index = intensity_index * np.sqrt(total_affected / total_stations)
                
                # 添加到过程汇总
                process_summary.append({
                    '过程ID': pid,
                    '开始日期': start_date,
                    '结束日期': end_date,
                    '持续天数': (end_date - start_date).days,
                    '中等强度冷空气站点数': n1,
                    '强冷空气站点数': n2,
                    '寒潮站点数': n3,
                    '影响总站点数': total_affected,
                    '影响站点比例': total_affected / total_stations,
                    '强度指数': intensity_index,
                    '强度等级': intensity_level,
                    '综合强度指数': comprehensive_index
                })
    
    process_summary_df = pd.DataFrame(process_summary)
    
    print(f"共识别出 {len(process_summary)} 次区域冷空气过程")
    return daily_stats, process_summary_df


# 导出结果
def export_results(df, daily_stats, process_summary_df, output_dir=None):
    """
    导出冷空气过程分析结果，使用中文表头并保留两位小数
    """
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(file_path), "results")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # --- 1. 导出站点冷空气等级数据 ---
    station_level_df = df.copy()
    
    # 需要保留两位小数的列
    station_numeric_cols = ['最低气温', 'dT24', 'dT48', 'dT72']
    for col in station_numeric_cols:
        if col in station_level_df.columns:
            station_level_df[col] = station_level_df[col].round(2)
            
    # 定义中文表头
    station_level_headers = {
        '区站号(字符)': '站点ID',
        '站名': '站名',
        '资料时间': '日期',
        '最低气温': '最低气温',
        'dT24': '24小时降温',
        'dT48': '48小时降温',
        'dT72': '72小时降温',
        'cold_air_level_name': '冷空气等级'
    }
    
    # 选择需要的列并重命名
    # 使用 list(station_level_headers.keys()) 保证顺序
    station_level_df = station_level_df[list(station_level_headers.keys())].rename(columns=station_level_headers)
    
    station_level_file = os.path.join(output_dir, "站点冷空气等级.csv")
    station_level_df.to_csv(station_level_file, index=False, encoding='utf-8-sig')

    # --- 2. 导出每日冷空气站点统计数据 ---
    daily_stats_df = daily_stats.copy()
    
    # 定义中文表头
    daily_stats_headers = {
        '资料时间': '日期',
        'medium_count': '中等强度站点数',
        'strong_count': '强冷空气站点数',
        'severe_count': '寒潮站点数',
        'total_affected': '总影响站点数'
    }
    
    # 选择需要的列并重命名
    # 筛选出存在的列进行操作
    cols_to_select = [col for col in daily_stats_headers.keys() if col in daily_stats_df.columns]
    daily_stats_df = daily_stats_df[cols_to_select].rename(columns=daily_stats_headers)
    
    daily_stats_file = os.path.join(output_dir, "每日冷空气站点统计.csv")
    daily_stats_df.to_csv(daily_stats_file, index=False, encoding='utf-8-sig')

    # --- 3. 导出冷空气过程汇总 ---
    process_summary_df_export = process_summary_df.copy()
    
    # 需要保留两位小数的列
    process_numeric_cols = ['影响站点比例', '强度指数', '综合强度指数']
    for col in process_numeric_cols:
        if col in process_summary_df_export.columns:
            process_summary_df_export[col] = process_summary_df_export[col].round(2)
            
    # 表头已经是中文，所以直接导出
    process_summary_file = os.path.join(output_dir, "冷空气过程汇总.csv")
    process_summary_df_export.to_csv(process_summary_file, index=False, encoding='utf-8-sig')
    
    print(f"结果已导出至目录: {output_dir}")
    print(f"1. 站点冷空气等级: {station_level_file}")
    print(f"2. 每日冷空气站点统计: {daily_stats_file}")
    print(f"3. 冷空气过程汇总: {process_summary_file}")

# 主函数
def main():
    # 读取数据
    df = read_data(file_path)
    
    # 计算降温幅度
    df = calculate_temperature_drops(df)
    
    # 判断单站冷空气等级
    df = determine_cold_air_level(df)
    
    # 识别区域冷空气过程
    daily_stats, process_summary_df = identify_regional_cold_air_process(df)
    
    # 输出区域冷空气过程汇总
    if not process_summary_df.empty:
        print("\n区域冷空气过程汇总:")
        pd.set_option('display.max_columns', None)
        print(process_summary_df)
        
        output_dir = os.path.join(os.path.dirname(file_path), "results")
        os.makedirs(output_dir, exist_ok=True)
        
        # 导出结果
        export_results(df, daily_stats, process_summary_df, output_dir)
    else:
        print("未识别出区域冷空气过程")

if __name__ == "__main__":
    main()
