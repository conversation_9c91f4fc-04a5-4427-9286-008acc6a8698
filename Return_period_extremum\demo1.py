import numpy as np

# 读取数据文件
data = np.loadtxt(r"D:\Data verification\Return_period_extremum\重现期验证数据\56247_1440min_copy.txt", dtype=str, delimiter="\t")
values = data[:, 1].astype(float)  

# 计算样本均值和标准差
sample_mean = np.mean(values)
sample_std = np.std(values, ddof=1)  # 无偏估计

# 矩估计公式
gamma = 0.5772  
sigma_mom = sample_std * np.sqrt(6) / np.pi
mu_mom = sample_mean - sigma_mom * gamma

print(f"矩估计结果: mu = {mu_mom:.3f}, sigma = {sigma_mom:.3f}")


from scipy.optimize import minimize

# 定义负对数似然函数（因为优化器默认最小化）
def neg_log_likelihood(params, data):
    mu, sigma = params
    if sigma <= 0:
        return np.inf  # 返回无穷大避免无效参数
    z = (data - mu) / sigma
    return np.sum(z + np.exp(-z)) + len(data) * np.log(sigma)

# 初始值设为矩估计结果
initial_guess = [mu_mom, sigma_mom]

# 调用优化器
result = minimize(
    neg_log_likelihood,
    initial_guess,
    args=(values),
    bounds=[(None, None), (1e-6, None)]  
)

mu_mle, sigma_mle = result.x
print(f"最大似然估计结果: mu = {mu_mle:.3f}, sigma = {sigma_mle:.3f}")

# %%
from scipy.stats import gumbel_r

# SciPy的Gumbel_r对应Gumbel Max分布
fit_mu, fit_sigma = gumbel_r.fit(values)
print(f"SciPy拟合结果: mu = {fit_mu:.3f}, sigma = {fit_sigma:.3f}")

# %%
