import pandas as pd
import numpy as np
from scipy.stats import gumbel_r, kstest, chisquare, anderson_ksamp,PermutationMethod
from sklearn.metrics import mean_squared_error

def gumbel_mle_fit(data):
    params = gumbel_r.fit(data)
    return params[0], params[1] 

def calculate_return_level(mu, beta, return_period):

    # 计算非超越概率
    non_exceedance_prob = 1 - 1 / return_period
    # 计算对应的分位数
    return gumbel_r.ppf(non_exceedance_prob, loc=mu, scale=beta)

def MT_KStest(x1, x2, alpha):
    res = kstest(x1, x2)
    svalue = res.statistic
    if res.pvalue <= alpha:
        m = 1
    else:
        m = 0
    return svalue, m

def ADtest_multi(x1, x2, alpha):
    samples = [x1, x2]
    res = anderson_ksamp(samples,method=PermutationMethod())
    pvalue = res.pvalue
    svalue = res.statistic
    if pvalue < alpha:
        m = 1
    else:
        m = 0
    return svalue, m

def MT_CHItest(x1, x2):
    obs = np.array([x1, x2])
    data1, _ = np.histogram(x1, bins=20, range=(np.min(obs), np.max(obs)))
    data2, _ = np.histogram(x2, bins=20, range=(np.min(obs), np.max(obs)))
    data1 = np.array(data1) + 0.1
    data2 = np.array(data2) + 0.1

    res = chisquare(f_obs=data1, f_exp=data2)
    svalue = res.statistic
    if res.pvalue <= 0.01:
        m = 0.01
    elif res.pvalue <= 0.05 and res.pvalue > 0.01:
        m = 0.05
    elif res.pvalue <= 0.1 and res.pvalue > 0.05:
        m = 0.1
    else:
        m = 1
    return svalue, m

def mse_test(x1, x2):
    mse = mean_squared_error(x1, x2)
    return mse, 0


def rmse_test(x1, x2):
    """
    计算均方根误差（RMSE）

    参数:
    x1 (numpy array): 实际值
    x2 (numpy array): 预测值

    返回:
        float: 均方根误差
    """
    # 计算差值的平方
    squared_errors = (x1 - x2) ** 2

    # 计算平方的平均值
    mean_squared_error = np.mean(squared_errors)

    # 取平方根
    rmse = np.sqrt(mean_squared_error)

    return rmse, 0



# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path = r'D:\python\Return_period_extremum\test_data\1991~2020年德阳降水年际变化统计图.xlsx'
    T = pd.read_excel(file_path)

    precip = T['降水']

    # 拟合耿贝尔分布
    mu, beta = gumbel_mle_fit(precip)
    #print(f"Estimated parameters: mu = {mu:.2f}, beta = {beta:.2f}")

    return_period = np.arange(0.001, 
                              1.001, 
                              1.001/63)
    fitted_data = gumbel_r.ppf(return_period, loc=mu, scale=beta)
    #print(f"Fitted data: {fitted_data}")



    # 进行三种检验
    alpha = 0.05
    ks_svalue, ks_result = MT_KStest(precip, fitted_data, alpha)
    ad_svalue, ad_result = ADtest_multi(precip, fitted_data, alpha)
    chi_svalue, chi_m = MT_CHItest(precip, fitted_data)
    mse, mse_m = mse_test(precip, fitted_data)
    rmse, rmse_m = rmse_test(precip, fitted_data)
    
    print(f"KS test statistic: {ks_svalue}, KS test result: {ks_result}")
    print(f"AD test statistic: {ad_svalue},AD multi-sample test result: {ad_result}")
    print(f"Chi-square test statistic: {chi_svalue}, p-value level: {chi_m}")
    print(f"MSE: {mse}, MSE result: {mse_m}")
    print(f"RMSE: {rmse}, RMSE result: {rmse_m}")

    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\End_Result\gumbel.txt", "w", encoding='utf-8') as file:
        file.write(f"位置参数 = {mu:.3f}, 尺度参数 = {beta:.3f} \n")
        for rp in return_periods:
            rl = calculate_return_level(mu, beta, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm \n")
