import pandas as pd
import numpy as np
from scipy.stats import expon

def exponential_fit(data):
    """
    使用最大似然估计拟合指数分布参数
    :param data: 年最大降水序列
    :return: 尺度参数scale (1/λ)
    """
    # 指数分布的MLE估计就是样本均值
    loc , scale = expon.fit(data)
    return loc, scale

def calculate_exponential_return_level(loc, scale, return_period):
    """
    计算给定重现期的降水极值
    :param loc: 位置参数
    :param scale: 尺度参数 (1/λ)
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return expon.ppf(non_exceedance_prob, loc=loc, scale=scale)


# 示例使用
if __name__ == "__main__":
    # 读取数据
    file_path = r'D:\python\Return_period_extremum\test_data\1991~2020年德阳降水年际变化统计图.xlsx'
    T = pd.read_excel(file_path)

    year = T['年份']
    precip = T['降水']
    
    # 拟合指数分布
    loc, scale = exponential_fit(precip)
    print(f"Estimated parameter: loc = {loc:.2f} scale = {scale:.2f}")

    
    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\End_Result\exponential.txt", "w", encoding='utf-8') as file:
        file.write(f"位置参数 = {loc:.3f}, 尺度参数 = {scale:.3f} \n")
        for rp in return_periods:
            rl = calculate_exponential_return_level(loc, scale, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")
