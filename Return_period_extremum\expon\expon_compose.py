import pandas as pd
import numpy as np

def exponential_fit(data):
    """
    使用最大似然估计拟合双参数指数分布
    :param data: 年最大降水序列
    :return: (gamma, lambda_) 位置参数和速率参数
    """
    gamma = np.min(data)  # 位置参数MLE估计为样本最小值
    lambda_ = 1 / (np.mean(data) - gamma)  # 速率参数MLE估计
    return gamma, lambda_

def calculate_exponential_return_level(gamma, lambda_, return_period):
    """
    计算给定重现期的降水极值（手动实现分位点函数）
    :param gamma: 位置参数
    :param lambda_: 速率参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 手动实现分位点公式：F^{-1}(p) = γ - ln(1-p)/λ
    return gamma - np.log(1 - non_exceedance_prob) / lambda_

# 示例使用
if __name__ == "__main__":
    # 读取数据（保持原样）
    file_path = r'D:\python\Return_period_extremum\Bureau_data\56247_1440min.txt'
    T = pd.read_csv(file_path,sep='\t',engine='python').values

    precip = T[:,1]
    
    # 拟合指数分布（现在返回gamma和lambda）
    gamma, lambda_ = exponential_fit(precip)
    print(f"估计参数: γ = {gamma:.2f}, λ = {lambda_:.2f}")

    # 计算不同重现期的降水极值
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    with open(r"D:\python\Return_period_extremum\Bureau_data\End_result\compose\exponential.txt", "w", encoding='utf-8') as file:
        file.write(f"位置参数 γ = {gamma:.3f}, 速率参数 λ = {lambda_:.3f} \n")
        for rp in return_periods:
            rl = calculate_exponential_return_level(gamma, lambda_, rp)
            print(f"{rp}-year return level: {rl:.2f} mm")
            file.write(f"{rp}-year return level: {rl:.1f} mm\n")