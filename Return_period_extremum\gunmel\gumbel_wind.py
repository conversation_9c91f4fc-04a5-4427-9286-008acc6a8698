import pandas as pd
import numpy as np
from scipy.stats import gumbel_r


def gumbel_mle_fit(data):
    """
    使用最大似然估计拟合耿贝尔分布参数
    """
    params = gumbel_r.fit(data)
    return params[0], params[1]  # mu, beta


def calculate_return_level(mu, beta, return_period):
    """
    计算给定重现期的降水极值
    :param mu: 位置参数
    :param beta: 尺度参数
    :param return_period: 重现期(年)
    :return: 重现期对应的降水极值
    """
    # 计算非超越概率
    non_exceedance_prob = 1 - 1/return_period
    # 计算对应的分位数
    return gumbel_r.ppf(non_exceedance_prob, loc=mu, scale=beta)


def calculate_wind_speed(v1, z1, z2, alpha):
    return v1 * (z2 / z1) ** alpha


# 示例使用
if __name__ == "__main__":

    # 读取 Excel 文件
    file_path = r'D:\python\Return_period_extremum\test_data\Tem_PRS_Wind_E_day.xlsx'
    try:
        T = pd.read_excel(file_path)

        # 确保日期列被正确解析为datetime
        time = pd.to_datetime(T['资料时间'], errors='coerce')

        # 删除无效日期
        valid_dates = ~time.isna()
        time = time[valid_dates]

        # 提取数据列
        wind_speed = T['极大风速'][valid_dates]

        # 未去除999999的缺失值
        print("未去除缺失值前：")
        print(wind_speed.shape)

        wind_speed = wind_speed[~(wind_speed == 999999)]

        # 去除999999的缺失值
        print("去除缺失值后：")
        print(wind_speed.shape)

        # 按年份分类，取每年最大风速
        wind_speed = wind_speed.groupby(time.dt.year).max()

        # 按年份分类，取每年最大风速，并输出对应年份
        print("按年份分类后：")
        print(wind_speed.shape)
        print("对应年份：")
        print(wind_speed.index)


    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 未找到。")
    except Exception as e:
        print(f"发生未知错误: {e}")

    # 风速高度换算
    #wind_speed = calculate_wind_speed(wind_speed, 10.0, 50.0, 0.16)

    
    # 拟合耿贝尔分布
    mu, beta = gumbel_mle_fit(wind_speed)
    print(f"Estimated parameters: mu = {mu:.2f}, beta = {beta:.2f}")
    
    #输出重现期
    return_periods = [2, 5, 10, 20, 30, 50, 100]
    for rp in return_periods:
        rl = calculate_return_level(mu, beta, rp)
        print(f"{rp}-year return level: {rl:.1f} m/s")
   